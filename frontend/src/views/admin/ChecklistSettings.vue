<template>
  <div class="checklist-settings">
    <div class="page-header">
      <h1>CheckList配置</h1>
      <p>从Excel文件批量导入检查项，快速创建和配置模板</p>
    </div>
    
    <div class="page-content">
      <ExcelImport @go-to-templates="handleGoToTemplates" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue'
import { useRouter } from 'vue-router'
import ExcelImport from '@/components/admin/ExcelImport.vue'

const router = useRouter()

const handleGoToTemplates = () => {
  // 跳转到模板编辑页面
  router.push('/admin/templates')
}

// Set page title
onMounted(() => {
  document.title = 'CheckList配置 - Checklist Review System'
})
</script>

<style scoped>
.checklist-settings {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 20px;
}

.page-header {
  background-color: #fff;
  padding: 30px;
  margin-bottom: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.page-header h1 {
  color: #303133;
  margin: 0 0 10px 0;
  font-size: 28px;
  font-weight: 600;
}

.page-header p {
  color: #606266;
  margin: 0;
  font-size: 16px;
  line-height: 1.5;
}

.page-content {
  background-color: #fff;
  padding: 30px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}
</style>
